SELECT T.PRE_ID,
       T.TRADE_DT TRADEDT,
       DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) ACCOUNTDT,
       T2.CONSCUSTNO,
       T2.CONSCUSTNA<PERSON> CUSTNAME,
       preAct.CREATOR CONSCODE,
       T2.PCODE FUNDCODE,
       JJXX.JJJ<PERSON> FUNDNAME,
       T5.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
       nvl(T.REALPAY_AMT_RMB, nvl(preAct.realpayamt_rmb, preAct.realpayamt)) ACKAMTRMB,
       CASE
           WHEN preAct.CURRENCY <> '156' THEN
                   preAct.FEE *
                   NVL(RMBZJJ.ZJJ,
                       (SELECT ZZJJ.ZJJ
                        FROM docker_it34_cust.RMBHLZJJ ZZJJ
                        WHERE ZZJJ.DHBZ = preAct.CURRENCY
                          AND ZZJJ.JZRQ IN
                              (SELECT MAX(ZJJ.JZRQ)
                               FROM docker_it34_cust.R<PERSON><PERSON><PERSON>JJ ZJJ
                               WHERE ZJJ.DHBZ = preAct.CURRENCY
                                 AND ZJJ.JZRQ <= preAct.EXPECTTRADEDT)))
		   ELSE
			   preAct.FEE
		   END ACKFEERMB,
	   CASE
		   WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
				(T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
				(T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
			   T3.DISCOUNT_TYPE
		   ELSE
			   CASE
				   WHEN T3.DISCOUNT_TYPE IS NOT NULL THEN
					   '4'
				   ELSE
					   ''
				   END
		   END DISCOUNTTYPE,
	   CASE
		   WHEN T2.CURRENCY != '156' THEN
				   NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT) * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
		   ELSE
			   NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT)
		   END DISCOUNTFEE,
	   CASE
		   WHEN T2.CURRENCY != '156' THEN
				   T3.BEF_TAX_AMT_ADJUST * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
		   ELSE
			   T3.BEF_TAX_AMT_ADJUST
		   END ADJUSTDISCOUNTFEE,
	   T3.DISCOUNT_REASON DISCOUNTREASON,
	   T.TRADE_NUM TRADENUM,
	   T2.SOURCETYPE,
	   CASE
		   WHEN T3.ID IS NOT NULL AND ((T3.DISCOUNT_TYPE = '6' and T3.DISCOUNT_STATE = '5')
		   								or (T3.DISCOUNT_TYPE = '8' and T3.DISCOUNT_STATE IN ('3', '5'))) THEN
			   1
		   ELSE
			   (CASE
					WHEN T.MANY_CALL_FLAG = '2' THEN
						NVL(CFCOEFF.SOURCECOEFF, 1)
					ELSE
						NVL(T1.CUST_SORCE_COEFF, T7.SOURCE_COEFF)
				   END)
		   END AS SOURCECOEFF,

	NVL(T1.COMMISSION_RATE,
	(CASE
	WHEN T.MANY_CALL_FLAG = '2' THEN
	-- 后续call预约的佣金率，如果换投顾，则取0.1（20250411惠真提的）
	(case when CFCOEFF.COMMISSIONRATE is null and (t.REAL_PRE_ID = T.PRE_ID or t.cons_code != T2.creator) then 0.1 else CFCOEFF.COMMISSIONRATE end)
	ELSE
		(CASE
		WHEN
		(T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') THEN
			NVL(ORDERCOEFF.COMMISSION_RATE, 0)
		ELSE
		T5.COMMISSION_RATE
		END)
	END)) AS COMMISSIONRATE,
	T1.MANAGE_COEFF manageCoeff,
	T1.MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
	T1.MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
	CASE WHEN T.MANY_CALL_FLAG = '2' AND (t.REAL_PRE_ID = T.PRE_ID or t.cons_code != T2.creator) then '1'
		 else '0'
	end  as tansConsManycall,
	CASE WHEN t.MANY_CALL_FLAG = '2' AND t.cons_code = T2.creator then T2.id else preAct.id end  as coeffPreId,
	CASE WHEN t.MANY_CALL_FLAG = '2' AND t.cons_code = T2.creator then T2.credt else preAct.credt end as creDt,
	DECODE(T1.ACCOUNT_DT, NULL, NULL, '1') accountDtFlag,
	DECODE(T1.COMMISSION_RATE, NULL, NULL, '1') commissionRateFlag,
	CASE WHEN T2.CURRENCY != '156' THEN
			manycall.TOTALAMT *
					NVL(RMBZJJ.ZJJ,
						(SELECT ZZJJ.ZJJ FROM docker_it34_cust.RMBHLZJJ ZZJJ WHERE ZZJJ.DHBZ = T2.CURRENCY
							AND ZZJJ.JZRQ IN (SELECT MAX(ZJJ.JZRQ) FROM docker_it34_cust.RMBHLZJJ ZJJ WHERE ZJJ.DHBZ = T2.CURRENCY
												AND ZJJ.JZRQ <= T2.EXPECTTRADEDT
											  )
						)
					)
	ELSE
		manycall.TOTALAMT
	END SUBSCRIBE_AMT_RMB,
	T.REAL_PRE_ID realpreId,
	T2.EXPECTTRADEDT expectTradeDt,
	T2.PAYSTATE payState
	FROM docker_it34_cust.CM_TRADE_NUM_SUMMARY T
     LEFT JOIN docker_it34_cust.CM_PRP_PREID_EXP_COEFF T1
               ON T.PRE_ID = T1.PREID
     LEFT JOIN docker_it34_cust.CM_PREBOOKPRODUCTINFO T2
               ON T.REAL_PRE_ID = T2.ID
     LEFT JOIN docker_it34_cust.CM_PREBOOKPRODUCTINFO preAct
               ON T.PRE_ID = preAct.ID
	left join docker_it34_cust.cm_prebook_manycall manycall
			   on T.REAL_PRE_ID = manycall.FIRSTPREID
     LEFT JOIN docker_it34_cust.CM_DISCOUNTAPP T3
               ON T.PRE_ID = T3.PREBOOKID
                   AND T3.DISCOUNT_STATE != '7'
     LEFT JOIN docker_it34_cust.CM_CONSULTANT T4
               ON T2.CREATOR = T4.CONSCODE
     LEFT JOIN docker_it34_cust.CM_PRP_PRODUCT_COEFFICIENT T5
               ON T2.PCODE = T5.FUNDCODE
                   AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) >= T5.START_DT
                   AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) <= T5.End_Dt
     LEFT JOIN docker_it34_cust.CM_PRP_CUST_SOURCE_COEFF CPCSC
               ON T2.SOURCETYPE = CPCSC.SOURCE_TYPE
                   AND T.TRADE_DT >= CPCSC.START_DT
                   AND T.TRADE_DT <= NVL(CPCSC.END_DT, '********')
     LEFT JOIN docker_it34_cust.CM_PRP_SOURCE_COEFFICIENT T7
               ON CPCSC.START_POINT = T7.START_POINT
                   AND DECODE(T.TRADE_NUM, 0, '0', 1, '1', 2, '2', 3, '3', 4, '4', '99') =
                       T7.TRADE_NUM
     LEFT JOIN docker_it34_cust.JJXX1 JJXX
               ON T2.PCODE = JJXX.JJDM
     LEFT JOIN docker_it34_cust.RMBHLZJJ RMBZJJ
               ON T2.CURRENCY = RMBZJJ.DHBZ
                   AND T.TRADE_DT = RMBZJJ.JZRQ
                   AND T2.CURRENCY != '156'
     LEFT JOIN docker_it34_cust.CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
               ON T3.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
                   AND T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
            		and T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.Big_Order_Product_Type
                   AND T.TRADE_DT >= ORDERCOEFF.START_DT
                   AND T.TRADE_DT <= NVL(ORDERCOEFF.END_DT, '********')
                   and t5.STOCK_FEE_A >= nvl(ORDERCOEFF.stock_fee_d_min,0) and T5.STOCK_FEE_A < nvl(ORDERCOEFF.stock_fee_d_max,10000)
     LEFT JOIN (SELECT PREBOOK.ID,
                       NVL(EXPCOEFF.UNQUALIFIED_COEFF, 1) AS UNQUALIFIEDCOEFF,
                       NVL(EXPCOEFF.COMMISSION_RATE,
							(CASE
							WHEN
							(DISCOUNT.DISCOUNT_TYPE = '6' AND
								DISCOUNT.DISCOUNT_STATE = '5')  THEN
							NVL(ORDERCOEFF.COMMISSION_RATE, 0)
							ELSE
							PRODUCTCOEFF.COMMISSION_RATE
							END)) AS COMMISSIONRATE,

                       NVL(EXPCOEFF.CUST_SORCE_COEFF, CPSC.SOURCE_COEFF) AS SOURCECOEFF,
                       NVL(EXPCOEFF.MANAGE_COEFF, CPCSC.MANAGE_COEFF) AS MANAGECOEFF
                FROM docker_it34_cust.CM_PREBOOKPRODUCTINFO PREBOOK
                         LEFT JOIN docker_it34_cust.CM_DISCOUNTAPP DISCOUNT
                                   ON PREBOOK.ID = DISCOUNT.PREBOOKID
                                       AND DISCOUNT.DISCOUNT_STATE != '7'
                         INNER JOIN (
	select PREID as PRE_ID,trade_dt as TRADE_DT,TRADE_NUM,MANYCALL_FLAG as MANY_CALL_FLAG,REALPREID from docker_it34_cust.CM_PRP_TRADE_NUM
	union
	select PRE_ID,TRADE_DT,TRADE_NUM,MANY_CALL_FLAG,REAL_PRE_ID as REALPREID from docker_it34_cust.CM_TRADE_NUM_SUMMARY) CPTN
                                    ON CPTN.PRE_ID = PREBOOK.ID
                         LEFT JOIN docker_it34_cust.CM_PRP_PREID_EXP_COEFF EXPCOEFF
                                   ON PREBOOK.ID = EXPCOEFF.PREID
                         INNER JOIN docker_it34_cust.CM_CONSULTANT CONS
                                    ON PREBOOK.CREATOR = CONS.CONSCODE
                         LEFT JOIN docker_it34_cust.CM_PRP_PRODUCT_COEFFICIENT PRODUCTCOEFF
                                   ON PREBOOK.PCODE = PRODUCTCOEFF.FUNDCODE
                                       AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) >= PRODUCTCOEFF.START_DT
                                       AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) <= PRODUCTCOEFF.End_Dt
                         LEFT JOIN docker_it34_cust.CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
                                   ON DISCOUNT.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
                                       AND PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE =
                                           ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
                						and PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.Big_Order_Product_Type
                                       AND CPTN.TRADE_DT >= ORDERCOEFF.START_DT
                                       AND CPTN.TRADE_DT <= NVL(ORDERCOEFF.END_DT, '********')
                                       and PRODUCTCOEFF.STOCK_FEE_A >= nvl(ORDERCOEFF.stock_fee_d_min,0) and PRODUCTCOEFF.STOCK_FEE_A < nvl(ORDERCOEFF.stock_fee_d_max,10000)
                         LEFT JOIN docker_it34_cust.CM_PRP_CUST_SOURCE_COEFF CPCSC
                                   ON PREBOOK.SOURCETYPE = CPCSC.SOURCE_TYPE
                                       AND CPTN.TRADE_DT >= CPCSC.START_DT
                                       AND CPTN.TRADE_DT <= NVL(CPCSC.END_DT, '********')
                         LEFT JOIN docker_it34_cust.CM_PRP_SOURCE_COEFFICIENT CPSC
                                   ON DECODE(CPTN.TRADE_NUM,
                                             0,
                                             '0',
                                             1,
                                             '1',
                                             2,
                                             '2',
                                             3,
                                             '3',
                                             4,
                                             '4',
                                             '99') = CPSC.TRADE_NUM
                                       AND CPSC.START_POINT = CPCSC.START_POINT
                WHERE CPTN.MANY_CALL_FLAG = '1') CFCOEFF
               ON T.REAL_PRE_ID = CFCOEFF.ID
		WHERE
		t.trade_num_type in ('1','2')
		and (T5.ACCOUNT_PRODUCT_TYPE not in ('8','12','13','14','15') OR T5.ACCOUNT_PRODUCT_TYPE IS NULL)
	order by T.TRADE_DT DESC;
